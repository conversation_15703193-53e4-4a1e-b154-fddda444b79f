/**
 * 笔记系统主模块
 * 负责笔记列表显示、分页、过滤等核心功能
 */

// 全局图片预览功能
let activeViewer = null;

function openImageFullscreen(src) {
    if (!src) return;
    console.log('[DEBUG] Opening image in fullscreen:', src);

    // 如果已经有活跃的预览，先关闭它
    if (activeViewer) {
        try {
            activeViewer.destroy();
            activeViewer = null;
            // 找到并移除可能存在的容器
            const oldContainer = document.querySelector('.viewer-container');
            if (oldContainer && oldContainer.parentNode) {
                oldContainer.parentNode.removeChild(oldContainer);
            }
        } catch (e) {
            console.error('Error destroying existing viewer:', e);
        }
    }

    // 创建临时图片元素用于预览
    const container = document.createElement('div');
    container.className = 'viewer-container';
    container.style.display = 'none';
    document.body.appendChild(container);

    const img = document.createElement('img');
    img.src = src;
    img.alt = '图片预览';
    container.appendChild(img);

    // 初始化 Viewer
    const viewer = new Viewer(img, {
        backdrop: true,          // 启用背景遮罩
        button: true,           // 显示关闭按钮
        navbar: false,          // 隐藏底部导航栏（只有一张图片时不需要）
        title: false,           // 不显示标题
        toolbar: {              // 自定义工具栏
            zoomIn: true,       // 放大按钮
            zoomOut: true,      // 缩小按钮
            oneToOne: true,     // 1:1 尺寸按钮
            reset: true,        // 重置按钮
            prev: false,        // 上一张（隐藏，因为只有一张图片）
            play: false,        // 播放按钮（隐藏）
            next: false,        // 下一张（隐藏）
            rotateLeft: true,   // 向左旋转
            rotateRight: true,  // 向右旋转
            flipHorizontal: true, // 水平翻转
            flipVertical: true,  // 垂直翻转
        },
        viewed() {
            // 图片加载完成后自动打开查看器
            if (window.innerWidth < 640) {
                viewer.zoomTo(0.8);  // 移动设备使用较小的初始缩放比例
            } else {
                viewer.zoomTo(1);    // 桌面设备使用正常的缩放比例
            }
        },
        hidden() {
            // 查看器关闭时清理资源
            if (container && container.parentNode) {
                container.parentNode.removeChild(container);
            }
            activeViewer = null;
        }
    });

    activeViewer = viewer;
    viewer.show();
}

class NotesManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.currentFilter = {};
        this.currentSort = 'updated_at';
        this.currentView = 'grid'; // grid 或 list
        this.userRole = null;
        this.userId = null;
        this.uploadedImages = []; // 存储上传的图片

        this.init();
    }
    
    async init() {
        console.log('初始化笔记系统...');

        // 验证用户身份
        await this.checkAuth();

        // 初始化UI
        this.initializeUI();

        // 确保主题正确应用
        this.applyTheme();

        // 加载数据
        await this.loadCategories();
        await this.loadTags();
        await this.loadNotes();

        // 绑定事件
        this.bindEvents();

        console.log('笔记系统初始化完成');
    }

    applyTheme() {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const shouldUseDark = savedTheme === 'dark' || (!savedTheme && prefersDark);

        console.log('应用主题:', { savedTheme, prefersDark, shouldUseDark });

        const html = document.documentElement;
        const body = document.body;

        if (shouldUseDark) {
            html.classList.add('dark');
            body.classList.add('dark');
            console.log('应用深色主题');
        } else {
            html.classList.remove('dark');
            body.classList.remove('dark');
            console.log('应用浅色主题');
        }

        this.updateThemeIcon();
    }
    
    async checkAuth() {
        try {
            const token = localStorage.getItem('token');
            if (!token) {
                window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
                return;
            }
            
            const response = await fetch('/api/validate-token', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('Token验证失败');
            }
            
            const data = await response.json();
            this.userRole = data.user.role;
            this.userId = data.user.id;
            
            // 更新UI显示用户信息
            document.getElementById('username').textContent = data.user.username;
            document.getElementById('userRole').textContent = this.userRole === 'admin' ? '管理员' : '普通用户';
            document.getElementById('userRole').className = `ml-2 px-2 py-1 rounded text-xs ${
                this.userRole === 'admin' ? 'bg-red-200 dark:bg-red-800 text-red-800 dark:text-red-200' : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
            }`;
            
            // 所有用户都可以创建笔记，但管理功能仅限管理员
            document.getElementById('newNoteBtn').classList.remove('hidden');
            document.getElementById('myNotesFilter').classList.remove('hidden');
            
            // 管理员特有功能
            if (this.userRole === 'admin') {
                document.getElementById('manageCategoriesBtn').classList.remove('hidden');
                document.getElementById('manageTagsBtn').classList.remove('hidden');
            }
            
        } catch (error) {
            console.error('身份验证失败:', error);
            this.showToast('身份验证失败，请重新登录', 'error');
            setTimeout(() => {
                window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
            }, 2000);
        }
    }
    
    initializeUI() {
        // 设置默认视图
        this.updateViewButtons();

        // 设置默认排序
        document.getElementById('sortSelect').value = this.currentSort;

        // 初始化主题切换功能
        this.initThemeToggle();

        // 初始化图片上传功能
        this.initImageUpload();

        // 初始化详情模态框
        this.initDetailModal();
    }

    initThemeToggle() {
        const themeToggleBtn = document.getElementById('themeToggleBtn');
        const themeIcon = document.getElementById('themeIcon');

        if (themeToggleBtn && themeIcon) {
            // 设置初始主题图标
            this.updateThemeIcon();

            // 绑定主题切换事件
            themeToggleBtn.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    toggleTheme() {
        const html = document.documentElement;
        const body = document.body;
        const isDark = html.classList.contains('dark');

        console.log('切换主题，当前是深色模式:', isDark);

        if (isDark) {
            // 切换到浅色主题
            html.classList.remove('dark');
            body.classList.remove('dark');
            localStorage.setItem('theme', 'light');
            console.log('已切换到浅色主题');
        } else {
            // 切换到深色主题
            html.classList.add('dark');
            body.classList.add('dark');
            localStorage.setItem('theme', 'dark');
            console.log('已切换到深色主题');
        }

        this.updateThemeIcon();

        // 强制重新渲染
        setTimeout(() => {
            this.forceStyleUpdate();
        }, 100);
    }

    forceStyleUpdate() {
        // 强制重新计算所有元素的样式
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
            el.offsetHeight; // 触发重新计算
        });
        console.log('强制更新样式完成');
    }

    initImageUpload() {
        const uploadBtn = document.getElementById('uploadBtn');
        const imageUpload = document.getElementById('imageUpload');
        const imagePreview = document.getElementById('imagePreview');

        if (uploadBtn && imageUpload) {
            uploadBtn.addEventListener('click', () => {
                imageUpload.click();
            });

            imageUpload.addEventListener('change', (e) => {
                this.handleImageUpload(e.target.files);
            });
        }
    }

    handleImageUpload(files) {
        const imagePreview = document.getElementById('imagePreview');
        if (!imagePreview) return;

        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                if (file.size > 5 * 1024 * 1024) { // 5MB限制
                    this.showError('图片大小不能超过5MB');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    const imageData = {
                        file: file,
                        dataUrl: e.target.result,
                        name: file.name
                    };

                    this.uploadedImages.push(imageData);

                    // 如果编辑器存在，使用编辑器的渲染方法
                    if (window.notesEditor && typeof window.notesEditor.renderAllImages === 'function') {
                        window.notesEditor.renderAllImages();
                    } else {
                        this.renderImagePreview();
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    renderImagePreview() {
        const imagePreview = document.getElementById('imagePreview');
        if (!imagePreview) return;

        if (this.uploadedImages.length === 0) {
            imagePreview.classList.add('hidden');
            return;
        }

        imagePreview.classList.remove('hidden');
        imagePreview.innerHTML = this.uploadedImages.map((img, index) => `
            <div class="image-preview-item">
                <img src="${img.dataUrl}" alt="${img.name}">
                <button type="button" onclick="notesManager.removeImage(${index})" class="image-preview-remove">
                    <i class="fas fa-times"></i>
                </button>
                <div class="image-preview-name">${img.name}</div>
            </div>
        `).join('');
    }

    removeImage(index) {
        this.uploadedImages.splice(index, 1);

        // 如果编辑器存在，使用编辑器的渲染方法
        if (window.notesEditor && typeof window.notesEditor.renderAllImages === 'function') {
            window.notesEditor.renderAllImages();
        } else {
            this.renderImagePreview();
        }
    }

    initDetailModal() {
        const closeDetailBtn = document.getElementById('closeDetailBtn');
        const editNoteBtn = document.getElementById('editNoteBtn');

        if (closeDetailBtn) {
            closeDetailBtn.addEventListener('click', () => {
                this.closeDetailModal();
            });
        }

        if (editNoteBtn) {
            // 默认先隐藏编辑按钮，只有确认权限后再显示
            editNoteBtn.classList.add('hidden');
            
            editNoteBtn.addEventListener('click', () => {
                const noteId = editNoteBtn.dataset.noteId;
                if (noteId) {
                    this.closeDetailModal();
                    this.editNote(noteId);
                }
            });
        }

        // 点击背景关闭模态框
        document.getElementById('noteDetailModal')?.addEventListener('click', (e) => {
            if (e.target.id === 'noteDetailModal') {
                this.closeDetailModal();
            }
        });
    }



    renderNoteDetail(note) {
        // 设置基本信息
        document.getElementById('detailTitle').textContent = note.title || '无标题';
        document.getElementById('detailCategory').textContent = note.category_name || '未分类';

        // 格式化时间显示
        const formatDate = (dateStr) => {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        document.getElementById('detailCreatedAt').textContent = formatDate(note.created_at);
        document.getElementById('detailUpdatedAt').textContent = formatDate(note.updated_at);
        document.getElementById('detailAuthor').textContent = note.author_name || '未知';
        document.getElementById('editNoteBtn').dataset.noteId = note.id;
        
        // 控制编辑按钮的显示
        const editButton = document.getElementById('editNoteBtn');
        // 判断是否有编辑权限（管理员或笔记作者）
        const isOwner = String(note.author_id) === String(this.userId);
        const canEdit = this.userRole === 'admin' || isOwner;
        
        if (canEdit) {
            editButton.classList.remove('hidden');
        } else {
            editButton.classList.add('hidden');
        }

        // 渲染标签
        const tagsContainer = document.getElementById('detailTagsContainer');
        const tagsElement = document.getElementById('detailTags');
        if (note.tags && note.tags.length > 0) {
            tagsContainer.classList.remove('hidden');
            tagsElement.innerHTML = note.tags.map(tag => {
                const tagName = typeof tag === 'string' ? tag : tag.name;
                const tagColor = typeof tag === 'object' && tag.color ? tag.color : '#6B7280';
                return `<span class="px-2 py-1 text-xs rounded-full text-white" style="background-color: ${tagColor}">${tagName}</span>`;
            }).join('');
        } else {
            tagsContainer.classList.add('hidden');
        }

        // 渲染图片
        const imagesContainer = document.getElementById('detailImagesContainer');
        const imagesElement = document.getElementById('detailImages');
        if (note.images && note.images.length > 0) {
            imagesContainer.classList.remove('hidden');
            imagesElement.innerHTML = note.images.map((img, index) => {
                // 确保图片路径正确
                const imgSrc = img.startsWith('/') ? img : `/${img}`;
                return `<div class="relative group cursor-pointer" onclick="openImageFullscreen('${imgSrc}')">
                    <img src="${imgSrc}" alt="笔记图片"
                         class="w-full h-32 object-cover rounded-lg hover:opacity-90 transition-opacity"
                         onerror="this.style.display='none'; console.error('图片加载失败:', '${imgSrc}');">
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center pointer-events-none">
                        <i class="fas fa-search-plus text-white text-xl opacity-0 group-hover:opacity-100 transition-opacity"></i>
                    </div>
                </div>`;
            }).join('');
        } else {
            imagesContainer.classList.add('hidden');
        }

        // 渲染内容
        const content = note.content || '暂无内容';
        document.getElementById('detailContent').innerHTML = `<div class="prose dark:prose-invert max-w-none">${this.formatContent(content)}</div>`;
    }

    formatContent(content) {
        // 简单的内容格式化，可以后续扩展为Markdown渲染
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">$1</code>');
    }

    closeDetailModal() {
        document.getElementById('noteDetailModal').classList.add('hidden');
        
        // 清理状态，确保下次打开时重新判断权限
        const editNoteBtn = document.getElementById('editNoteBtn');
        if (editNoteBtn) {
            // 默认隐藏编辑按钮
            editNoteBtn.classList.add('hidden');
            editNoteBtn.dataset.noteId = '';
        }
    }

    openCategoryManager() {
        // 创建分类管理模态框
        const modal = this.createCategoryManagerModal();
        document.body.appendChild(modal);
        this.loadCategoriesForManager();
    }

    createCategoryManagerModal() {
        const modal = document.createElement('div');
        modal.id = 'categoryManagerModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';

        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">分类管理</h2>
                    <button id="closeCategoryManager" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="p-6">
                    <!-- 添加新分类 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">添加新分类</label>
                        <div class="flex space-x-2">
                            <input type="text" id="newCategoryName" placeholder="分类名称"
                                   class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <button id="addCategoryBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 现有分类列表 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">现有分类</label>
                        <div id="categoryManagerList" class="space-y-2 max-h-60 overflow-y-auto">
                            <!-- 分类列表将在这里显示 -->
                        </div>
                    </div>
                </div>

                <div class="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
                    <button id="closeCategoryManagerBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors modal-close-btn">
                        关闭
                    </button>
                </div>
            </div>
        `;

        // 绑定事件
        modal.addEventListener('click', (e) => {
            if (e.target.id === 'categoryManagerModal') {
                this.closeCategoryManager();
            }
        });

        // 绑定关闭按钮事件
        modal.querySelector('#closeCategoryManager').addEventListener('click', () => {
            this.closeCategoryManager();
        });

        modal.querySelector('#closeCategoryManagerBtn').addEventListener('click', () => {
            this.closeCategoryManager();
        });

        modal.querySelector('#addCategoryBtn').addEventListener('click', () => {
            this.addCategory();
        });

        modal.querySelector('#newCategoryName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addCategory();
            }
        });

        return modal;
    }

    async loadCategoriesForManager() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/categories', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('获取分类失败');
            }

            const data = await response.json();
            this.renderCategoryManagerList(data.data);

        } catch (error) {
            console.error('加载分类失败:', error);
            this.showToast('加载分类失败', 'error');
        }
    }

    renderCategoryManagerList(categories) {
        const container = document.getElementById('categoryManagerList');
        if (!container) return;

        if (categories.length === 0) {
            container.innerHTML = `
                <div class="manager-empty">
                    <i class="fas fa-folder-open text-2xl mb-2 opacity-50"></i>
                    <div>暂无分类</div>
                </div>
            `;
            return;
        }

        container.innerHTML = categories.map(category => `
            <div class="manager-item group">
                <div class="manager-item-content">
                    <div class="manager-item-icon">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="manager-item-info">
                        <span class="manager-item-name">${category.name}</span>
                        <span class="manager-item-count">${category.note_count || 0} 篇笔记</span>
                    </div>
                </div>
                <button onclick="notesManager.deleteCategory(${category.id})"
                        class="manager-item-delete"
                        title="删除分类">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    async addCategory() {
        const nameInput = document.getElementById('newCategoryName');
        const name = nameInput.value.trim();

        if (!name) {
            this.showToast('请输入分类名称', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/categories', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ name })
            });

            if (!response.ok) {
                throw new Error('添加分类失败');
            }

            nameInput.value = '';
            this.showToast('分类添加成功', 'success');
            this.loadCategoriesForManager(); // 刷新管理列表
            this.loadCategoriesFilter(); // 刷新侧边栏分类列表

        } catch (error) {
            console.error('添加分类失败:', error);
            this.showToast('添加分类失败', 'error');
        }
    }

    async deleteCategory(categoryId) {
        // 显示自定义确认对话框
        this.showConfirmDialog(
            '确认删除分类',
            '确定要删除这个分类吗？注意：如果该分类下有笔记，将无法删除。',
            async () => {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`/api/notes/categories/${categoryId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error || '删除分类失败');
                    }

                    this.showToast('分类删除成功', 'success');
                    // 确保管理列表刷新
                    await this.loadCategoriesForManager();
                    // 刷新侧边栏分类列表
                    await this.loadCategoriesFilter();

                } catch (error) {
                    console.error('删除分类失败:', error);
                    this.showToast(error.message, 'error');
                }
            },
            { confirmText: '删除分类', confirmClass: 'bg-red-600 hover:bg-red-700' }
        );
    }

    closeCategoryManager() {
        const modal = document.getElementById('categoryManagerModal');
        if (modal) {
            modal.remove();
        }
    }

    openTagManager() {
        // 创建标签管理模态框
        const modal = this.createTagManagerModal();
        document.body.appendChild(modal);
        this.loadTagsForManager();
    }

    createTagManagerModal() {
        const modal = document.createElement('div');
        modal.id = 'tagManagerModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';

        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">标签管理</h2>
                    <button id="closeTagManager" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="p-6">
                    <!-- 添加新标签 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">添加新标签</label>
                        <div class="flex space-x-2">
                            <input type="text" id="newTagName" placeholder="标签名称"
                                   class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <button id="addTagBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 现有标签列表 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">现有标签</label>
                        <div id="tagManagerList" class="space-y-2 max-h-60 overflow-y-auto">
                            <!-- 标签列表将在这里显示 -->
                        </div>
                    </div>
                </div>

                <div class="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
                    <button id="closeTagManagerBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors modal-close-btn">
                        关闭
                    </button>
                </div>
            </div>
        `;

        // 绑定事件
        modal.addEventListener('click', (e) => {
            if (e.target.id === 'tagManagerModal') {
                this.closeTagManager();
            }
        });

        // 绑定关闭按钮事件
        modal.querySelector('#closeTagManager').addEventListener('click', () => {
            this.closeTagManager();
        });

        modal.querySelector('#closeTagManagerBtn').addEventListener('click', () => {
            this.closeTagManager();
        });

        modal.querySelector('#addTagBtn').addEventListener('click', () => {
            this.addTag();
        });

        modal.querySelector('#newTagName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addTag();
            }
        });

        return modal;
    }

    async loadTagsForManager() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/tags', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('获取标签失败');
            }

            const data = await response.json();
            this.renderTagManagerList(data.data);

        } catch (error) {
            console.error('加载标签失败:', error);
            this.showToast('加载标签失败', 'error');
        }
    }

    renderTagManagerList(tags) {
        const container = document.getElementById('tagManagerList');
        if (!container) return;

        if (tags.length === 0) {
            container.innerHTML = `
                <div class="manager-empty">
                    <i class="fas fa-tags text-2xl mb-2 opacity-50"></i>
                    <div>暂无标签</div>
                </div>
            `;
            return;
        }

        container.innerHTML = tags.map(tag => `
            <div class="manager-item group">
                <div class="manager-item-content">
                    <div class="manager-item-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div class="manager-item-info">
                        <span class="manager-item-name">${tag.name}</span>
                        <span class="manager-item-count">${tag.usage_count || 0} 篇笔记</span>
                    </div>
                </div>
                <button onclick="notesManager.deleteTag(${tag.id})"
                        class="manager-item-delete"
                        title="删除标签">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    async addTag() {
        const nameInput = document.getElementById('newTagName');
        const name = nameInput.value.trim();

        if (!name) {
            this.showToast('请输入标签名称', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/tags', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ name })
            });

            if (!response.ok) {
                throw new Error('添加标签失败');
            }

            nameInput.value = '';
            this.showToast('标签添加成功', 'success');
            this.loadTagsForManager(); // 刷新管理列表
            this.loadTagsFilter(); // 刷新侧边栏标签列表

        } catch (error) {
            console.error('添加标签失败:', error);
            this.showToast('添加标签失败', 'error');
        }
    }

    async deleteTag(tagId) {
        // 显示自定义确认对话框
        this.showConfirmDialog(
            '确认删除标签',
            '确定要删除这个标签吗？删除后相关笔记将失去该标签。',
            async () => {
                try {
                    const token = localStorage.getItem('token');
                    const response = await fetch(`/api/notes/tags/${tagId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (!response.ok) {
                        throw new Error('删除标签失败');
                    }

                    this.showToast('标签删除成功', 'success');
                    // 确保管理列表刷新
                    await this.loadTagsForManager();
                    // 刷新侧边栏标签列表
                    await this.loadTagsFilter();

                } catch (error) {
                    console.error('删除标签失败:', error);
                    this.showToast('删除标签失败', 'error');
                }
            },
            { confirmText: '删除标签', confirmClass: 'bg-red-600 hover:bg-red-700' }
        );
    }

    closeTagManager() {
        const modal = document.getElementById('tagManagerModal');
        if (modal) {
            modal.remove();
        }
    }

    updateThemeIcon() {
        const themeIcon = document.getElementById('themeIcon');
        const isDark = document.documentElement.classList.contains('dark');

        console.log('更新主题图标，当前是深色模式:', isDark);

        if (themeIcon) {
            if (isDark) {
                // 深色模式显示太阳图标（表示可以切换到浅色）
                themeIcon.className = 'fas fa-sun';
                themeIcon.style.color = '#d1d5db';
            } else {
                // 浅色模式显示月亮图标（表示可以切换到深色）
                themeIcon.className = 'fas fa-moon';
                themeIcon.style.color = '#4b5563';
            }
            console.log('主题图标已更新:', themeIcon.className);
        }
    }
    
    bindEvents() {
        // 搜索框
        const searchInput = document.getElementById('searchInput');
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 500);
        });
        
        // 排序选择
        document.getElementById('sortSelect').addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.loadNotes();
        });
        
        // 视图切换
        document.getElementById('gridViewBtn').addEventListener('click', () => {
            this.currentView = 'grid';
            this.updateViewButtons();
            this.renderNotes();
        });
        
        document.getElementById('listViewBtn').addEventListener('click', () => {
            this.currentView = 'list';
            this.updateViewButtons();
            this.renderNotes();
        });
        
        // 新建笔记按钮
        document.getElementById('newNoteBtn').addEventListener('click', () => {
            this.openNewNote();
        });

        // 管理分类按钮
        document.getElementById('manageCategoriesBtn').addEventListener('click', () => {
            this.openCategoryManager();
        });

        // 管理标签按钮
        document.getElementById('manageTagsBtn').addEventListener('click', () => {
            this.openTagManager();
        });
        
        // 快速过滤按钮
        document.getElementById('filterPinned').addEventListener('click', () => {
            this.applyFilter({ is_pinned: 'true' }, '置顶笔记');
        });
        
        document.getElementById('filterMyNotes').addEventListener('click', () => {
            // 任何用户都可以过滤显示自己的笔记
            this.applyFilter({ author_id: this.userId }, '我的笔记');
        });
        
        document.getElementById('filterRecent').addEventListener('click', () => {
            this.currentSort = 'updated_at';
            document.getElementById('sortSelect').value = this.currentSort;
            this.applyFilter({}, '最近更新');
        });
    }
    
    async loadCategories() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/categories', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('加载分类失败');
            }
            
            const data = await response.json();
            this.renderCategories(data.data);
            
        } catch (error) {
            console.error('加载分类失败:', error);
            this.showToast('加载分类失败', 'error');
        }
    }
    
    renderCategories(categories) {
        const container = document.getElementById('categoriesList');
        
        if (!categories || categories.length === 0) {
            container.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">暂无分类</div>';
            return;
        }
        
        const html = categories.map(category => `
            <div class="category-item" data-category-id="${category.id}">
                <div class="category-info">
                    <i class="${category.icon}" style="color: ${category.color}"></i>
                    <span>${category.name}</span>
                </div>
                <span class="category-count">${category.note_count}</span>
            </div>
        `).join('');
        
        container.innerHTML = html;
        
        // 绑定分类点击事件
        container.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', () => {
                const categoryId = item.dataset.categoryId;
                const categoryName = item.querySelector('.category-info span').textContent;
                
                // 更新活动状态
                container.querySelectorAll('.category-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                
                this.applyFilter({ category_id: categoryId }, `分类: ${categoryName}`);
            });
        });
    }
    
    async loadTags() {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch('/api/notes/tags', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('加载标签失败');
            }
            
            const data = await response.json();
            this.renderTags(data.data);
            
        } catch (error) {
            console.error('加载标签失败:', error);
            this.showToast('加载标签失败', 'error');
        }
    }
    
    renderTags(tags) {
        const container = document.getElementById('tagsList');
        
        if (!tags || tags.length === 0) {
            container.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm w-full">暂无标签</div>';
            return;
        }
        
        const html = tags.map(tag => `
            <span class="tag-cloud-item" data-tag-id="${tag.id}" style="background-color: ${tag.color}">
                ${tag.name}
                <span class="ml-1 text-xs opacity-75">${tag.usage_count}</span>
            </span>
        `).join('');
        
        container.innerHTML = html;
        
        // 绑定标签点击事件
        container.querySelectorAll('.tag-cloud-item').forEach(item => {
            item.addEventListener('click', () => {
                const tagId = item.dataset.tagId;
                const tagName = item.textContent.split(' ')[0]; // 去掉数量部分
                
                // 更新活动状态
                container.querySelectorAll('.tag-cloud-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                
                this.applyFilter({ tag_id: tagId }, `标签: ${tagName}`);
            });
        });
    }
    
    async loadNotes() {
        try {
            this.showLoading();
            
            const token = localStorage.getItem('token');
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                sort_by: this.currentSort,
                sort_order: 'DESC',
                ...this.currentFilter
            });
            
            const response = await fetch(`/api/notes?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('加载笔记失败');
            }
            
            const data = await response.json();
            this.notes = data.data;
            this.totalPages = data.pagination.pages;
            
            this.renderNotes();
            this.renderPagination(data.pagination);
            this.updateStats(data.pagination.total);
            
        } catch (error) {
            console.error('加载笔记失败:', error);
            this.showToast('加载笔记失败', 'error');
            this.hideLoading();
        }
    }
    
    renderNotes() {
        const container = document.getElementById('notesContainer');
        
        if (!this.notes || this.notes.length === 0) {
            container.innerHTML = `
                <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-sticky-note text-4xl mb-4"></i>
                    <div class="text-lg mb-2">暂无笔记</div>
                    <div class="text-sm">
                        ${this.userRole === 'admin' ? '点击上方"新建笔记"按钮创建第一篇笔记' : '管理员还没有创建任何笔记'}
                    </div>
                </div>
            `;
            return;
        }
        
        const html = this.notes.map(note => this.renderNoteCard(note)).join('');
        
        if (this.currentView === 'grid') {
            container.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        } else {
            container.className = 'space-y-4';
        }
        
        container.innerHTML = html;
        
        // 绑定笔记卡片事件
        this.bindNoteCardEvents();
        
        this.hideLoading();
    }
    
    renderNoteCard(note) {
        const createdDate = new Date(note.created_at).toLocaleDateString('zh-CN');
        const updatedDate = new Date(note.updated_at).toLocaleDateString('zh-CN');

        const tagsHtml = note.tags.map(tag =>
            `<span class="elegant-tag" style="--tag-color: ${tag.color}">${tag.name}</span>`
        ).join('');

        const categoryHtml = note.category_name ?
            `<div class="elegant-category" style="--category-color: ${note.category_color}">
                <i class="${note.category_icon}"></i>
                <span>${note.category_name}</span>
            </div>` :
            `<div class="elegant-category placeholder">
                <i class="fas fa-folder"></i>
                <span>未分类</span>
            </div>`;

        // 判断用户是否有编辑权限（管理员或笔记作者）
        const isOwner = String(note.author_id) === String(this.userId);
        const canEdit = this.userRole === 'admin' || isOwner;
        
        // 构建操作按钮
        let actionButtons = '';
        
        // 所有用户都可以查看详情
        actionButtons += `
            <button class="elegant-action-btn primary" onclick="notesManager.viewNote(${note.id})" title="查看详情">
                <i class="fas fa-arrow-right"></i>
            </button>
        `;
        
        // 只有作者和管理员可以编辑
        if (canEdit) {
            actionButtons += `
                <button class="elegant-action-btn edit" onclick="notesManager.editNote(${note.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
            `;
        }
        
        // 只有管理员可以置顶和删除
        if (this.userRole === 'admin') {
            actionButtons += `
                <button class="elegant-action-btn pin ${note.is_pinned ? 'active' : ''}" onclick="notesManager.togglePin(${note.id})" title="${note.is_pinned ? '取消置顶' : '置顶'}">
                    <i class="fas fa-thumbtack"></i>
                </button>
                <button class="elegant-action-btn delete" onclick="notesManager.deleteNote(${note.id})" title="删除">
                    <i class="fas fa-trash-alt"></i>
                </button>
            `;
        }

        return `
            <article class="elegant-note-card ${note.is_pinned ? 'is-pinned' : ''}" data-note-id="${note.id}" data-author-id="${note.author_id || ''}">
                ${note.is_pinned ? '<div class="pin-badge"><i class="fas fa-star"></i></div>' : ''}

                <header class="card-header">
                    <div class="title-row">
                        <h3 class="note-title">${note.title}</h3>
                        <div class="meta-info">
                            <span class="view-count">
                                <i class="fas fa-eye"></i>
                                ${note.view_count}
                            </span>
                        </div>
                    </div>

                    <div class="category-row">
                        ${categoryHtml}
                        <div class="author-info">
                            <i class="fas fa-user-circle"></i>
                            <span>${note.author_name}</span>
                        </div>
                    </div>
                </header>

                <main class="card-content">
                    <div class="content-preview">${note.content || '暂无内容'}</div>
                    ${note.tags.length > 0 ? `<div class="tags-container">${tagsHtml}</div>` : ''}
                </main>

                <footer class="card-footer">
                    <div class="time-info">
                        <time class="created-time" title="创建时间">
                            <i class="fas fa-calendar-plus"></i>
                            ${createdDate}
                        </time>
                        <time class="updated-time" title="更新时间">
                            <i class="fas fa-clock"></i>
                            ${updatedDate}
                        </time>
                    </div>

                    <div class="action-buttons">
                        ${actionButtons}
                    </div>
                </footer>
            </article>
        `;
    }
    
    bindNoteCardEvents() {
        // 笔记卡片点击事件（点击卡片主体区域查看笔记）
        document.querySelectorAll('.note-card').forEach(card => {
            card.addEventListener('click', (e) => {
                // 如果点击的是按钮，不触发卡片点击事件
                if (e.target.closest('.note-actions')) {
                    return;
                }
                
                const noteId = card.dataset.noteId;
                this.viewNote(noteId);
            });
        });
    }
    
    async viewNote(noteId) {
        try {
            // 先立即更新UI上的查看次数（乐观更新）
            const noteCard = document.querySelector(`.elegant-note-card[data-note-id="${noteId}"]`);
            if (noteCard) {
                const viewCountElement = noteCard.querySelector('.view-count');
                if (viewCountElement) {
                    // 从当前显示的计数增加1
                    const currentText = viewCountElement.textContent.trim();
                    const currentCount = parseInt(currentText) || 0;
                    const newCount = currentCount + 1;
                    viewCountElement.innerHTML = `<i class="fas fa-eye"></i> ${newCount}`;
                }
                
                // 同时更新notes数组中对应笔记的查看次数
                if (this.notes) {
                    const noteIndex = this.notes.findIndex(note => note.id == noteId);
                    if (noteIndex !== -1) {
                        // 增加内存中的查看次数
                        this.notes[noteIndex].view_count = (this.notes[noteIndex].view_count || 0) + 1;
                    }
                }
            }

            const token = localStorage.getItem('token');
            const response = await fetch(`/api/notes/${noteId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('获取笔记详情失败');
            }

            const data = await response.json();

            // 调试：打印返回的数据
            console.log('笔记详情数据:', data.data);
            console.log('当前用户ID:', this.userId, '笔记作者ID:', data.data.author_id, '类型:', typeof this.userId, typeof data.data.author_id);

            // 使用详情模态框显示笔记
            this.renderNoteDetail(data.data);
            document.getElementById('noteDetailModal').classList.remove('hidden');
            
            // 用服务器返回的实际次数更新UI（如果与乐观更新不一致）
            if (noteCard && data.data.view_count) {
                const viewCountElement = noteCard.querySelector('.view-count');
                if (viewCountElement) {
                    viewCountElement.innerHTML = `<i class="fas fa-eye"></i> ${data.data.view_count}`;
                }
                
                // 确保内存中的数据与服务器一致
                if (this.notes) {
                    const noteIndex = this.notes.findIndex(note => note.id == noteId);
                    if (noteIndex !== -1) {
                        this.notes[noteIndex].view_count = data.data.view_count;
                    }
                }
            }

        } catch (error) {
            console.error('查看笔记失败:', error);
            this.showToast('查看笔记失败', 'error');
        }
    }

    openNewNote() {
        // 所有用户都可以新建笔记，无需检查管理员权限
        
        // 确保编辑器已初始化
        if (!window.notesEditor) {
            // 如果编辑器类存在但实例未创建，创建实例
            if (typeof NotesEditor !== 'undefined') {
                window.notesEditor = new NotesEditor();
            } else {
                this.showToast('编辑器加载失败，请刷新页面重试', 'error');
                return;
            }
        }

        // 打开新建模态框
        if (window.notesEditor && typeof window.notesEditor.openModal === 'function') {
            window.notesEditor.openModal();
        } else {
            this.showToast('新建功能暂时不可用，请刷新页面重试', 'error');
        }
    }

    async editNote(noteId) {
        try {
            // 获取笔记详情以检查权限
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/notes/${noteId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('获取笔记信息失败');
            }
            
            const data = await response.json();
            const note = data.data;
            
            // 检查用户是否有编辑权限
            const isOwner = String(note.author_id) === String(this.userId);
            const canEdit = this.userRole === 'admin' || isOwner;
            
            if (!canEdit) {
                this.showToast('您没有权限编辑这篇笔记', 'error');
                return;
            }
            
            // 确保编辑器已初始化
            if (!window.notesEditor) {
                // 如果编辑器类存在但实例未创建，创建实例
                if (typeof NotesEditor !== 'undefined') {
                    window.notesEditor = new NotesEditor();
                } else {
                    this.showToast('编辑器加载失败，请刷新页面重试', 'error');
                    return;
                }
            }

            // 打开编辑模态框
            if (window.notesEditor && typeof window.notesEditor.editNote === 'function') {
                window.notesEditor.editNote(noteId);
            } else {
                this.showToast('编辑功能暂时不可用，请刷新页面重试', 'error');
            }
        } catch (error) {
            console.error('检查编辑权限失败:', error);
            this.showToast('操作失败', 'error');
        }
    }



    async togglePin(noteId) {
        // 置顶功能仍然只允许管理员使用
        if (this.userRole !== 'admin') {
            this.showToast('只有管理员可以置顶笔记', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`/api/notes/${noteId}/pin`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('切换置顶状态失败');
            }

            const data = await response.json();
            this.showToast(data.message, 'success');

            // 重新加载笔记列表
            await this.loadNotes();

        } catch (error) {
            console.error('切换置顶状态失败:', error);
            this.showToast('操作失败', 'error');
        }
    }

    async deleteNote(noteId) {
        // 获取笔记详情，检查是否是自己的笔记
        try {
            const token = localStorage.getItem('token');
            const noteResponse = await fetch(`/api/notes/${noteId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!noteResponse.ok) {
                throw new Error('获取笔记信息失败');
            }
            
            const noteData = await noteResponse.json();
            const note = noteData.data;
            
            // 检查权限：只有管理员或笔记作者可以删除
            const isOwner = String(note.author_id) === String(this.userId);
            const canDelete = this.userRole === 'admin' || isOwner;
            
            if (!canDelete) {
                this.showToast('您没有权限删除这篇笔记', 'error');
                return;
            }
            
            // 显示自定义确认对话框
            this.showConfirmDialog('确认删除笔记', '确定要删除这篇笔记吗？此操作不可恢复。', async () => {
                try {
                    const deleteResponse = await fetch(`/api/notes/${noteId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (!deleteResponse.ok) {
                        throw new Error('删除笔记失败');
                    }

                    const data = await deleteResponse.json();
                    this.showToast(data.message, 'success');

                    // 重新加载笔记列表
                    await this.loadNotes();

                } catch (error) {
                    console.error('删除笔记失败:', error);
                    this.showToast('删除失败', 'error');
                }
            }, { confirmText: '删除笔记', confirmClass: 'bg-red-600 hover:bg-red-700' });
            
        } catch (error) {
            console.error('检查笔记权限失败:', error);
            this.showToast('操作失败', 'error');
        }
    }

    // 显示自定义确认对话框
    showConfirmDialog(title, message, onConfirm, options = {}) {
        // 移除可能已存在的确认框
        const existingDialog = document.getElementById('customConfirmDialog');
        if (existingDialog) {
            existingDialog.remove();
        }

        const confirmText = options.confirmText || '确认';
        const cancelText = options.cancelText || '取消';
        const confirmClass = options.confirmClass || 'bg-red-600 hover:bg-red-700';

        // 创建确认对话框
        const dialog = document.createElement('div');
        dialog.id = 'customConfirmDialog';
        dialog.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        dialog.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold confirm-dialog-title">${title}</h3>
                </div>
                <div class="p-6">
                    <p class="confirm-dialog-message">${message}</p>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
                    <button id="cancelConfirmBtn" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors confirm-dialog-cancel-btn">
                        ${cancelText}
                    </button>
                    <button id="confirmBtn" class="px-4 py-2 text-white rounded-lg ${confirmClass} transition-colors">
                        ${confirmText}
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // 绑定事件
        document.getElementById('cancelConfirmBtn').addEventListener('click', () => {
            dialog.remove();
        });
        
        document.getElementById('confirmBtn').addEventListener('click', () => {
            if (onConfirm) onConfirm();
            dialog.remove();
        });
        
        // 点击背景也可以关闭
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) {
                dialog.remove();
            }
        });
    }

    applyFilter(filter, filterName) {
        this.currentFilter = filter;
        this.currentPage = 1;

        // 更新过滤器显示
        document.getElementById('currentFilter').textContent = filterName || '';

        // 清除其他活动状态
        document.querySelectorAll('.category-item.active, .tag-cloud-item.active').forEach(item => {
            item.classList.remove('active');
        });

        // 高亮显示当前选择的过滤按钮
        if (filterName === '置顶笔记') {
            document.getElementById('filterPinned').classList.add('active');
            document.getElementById('filterMyNotes').classList.remove('active');
            document.getElementById('filterRecent').classList.remove('active');
        } else if (filterName === '我的笔记') {
            document.getElementById('filterPinned').classList.remove('active');
            document.getElementById('filterMyNotes').classList.add('active');
            document.getElementById('filterRecent').classList.remove('active');
        } else if (filterName === '最近更新') {
            document.getElementById('filterPinned').classList.remove('active');
            document.getElementById('filterMyNotes').classList.remove('active');
            document.getElementById('filterRecent').classList.add('active');
        } else {
            document.getElementById('filterPinned').classList.remove('active');
            document.getElementById('filterMyNotes').classList.remove('active');
            document.getElementById('filterRecent').classList.remove('active');
        }

        this.loadNotes();
    }

    clearFilter() {
        this.currentFilter = {};
        this.currentPage = 1;

        // 清除过滤器显示
        document.getElementById('currentFilter').textContent = '';

        // 清除活动状态
        document.querySelectorAll('.category-item.active, .tag-cloud-item.active').forEach(item => {
            item.classList.remove('active');
        });

        this.loadNotes();
    }

    handleSearch(query) {
        if (query.trim()) {
            this.applyFilter({ search: query.trim() }, `搜索: ${query.trim()}`);
        } else {
            this.clearFilter();
        }
    }

    updateViewButtons() {
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (this.currentView === 'grid') {
            gridBtn.className = 'view-toggle-btn active';
            listBtn.className = 'view-toggle-btn';
        } else {
            gridBtn.className = 'view-toggle-btn';
            listBtn.className = 'view-toggle-btn active';
        }
    }

    renderPagination(pagination) {
        const container = document.getElementById('pagination');

        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination-container">';

        // 上一页
        if (pagination.page > 1) {
            html += `<button onclick="notesManager.goToPage(${pagination.page - 1})" class="pagination-btn" title="上一页">
                <i class="fas fa-chevron-left"></i>
            </button>`;
        }

        // 页码
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        if (startPage > 1) {
            html += `<button onclick="notesManager.goToPage(1)" class="pagination-btn" title="第1页">1</button>`;
            if (startPage > 2) {
                html += '<span class="pagination-ellipsis">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === pagination.page;
            html += `<button onclick="notesManager.goToPage(${i})" class="pagination-btn ${isActive ? 'active' : ''}" title="第${i}页">${i}</button>`;
        }

        if (endPage < pagination.pages) {
            if (endPage < pagination.pages - 1) {
                html += '<span class="pagination-ellipsis">...</span>';
            }
            html += `<button onclick="notesManager.goToPage(${pagination.pages})" class="pagination-btn" title="第${pagination.pages}页">${pagination.pages}</button>`;
        }

        // 下一页
        if (pagination.page < pagination.pages) {
            html += `<button onclick="notesManager.goToPage(${pagination.page + 1})" class="pagination-btn" title="下一页">
                <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadNotes();
    }

    updateStats(total) {
        document.getElementById('totalNotes').textContent = total;
    }

    showLoading() {
        const container = document.getElementById('notesContainer');
        container.innerHTML = `
            <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                <i class="fas fa-spinner fa-spin text-2xl"></i>
                <div class="mt-4 text-lg">加载中...</div>
            </div>
        `;
    }

    hideLoading() {
        // Loading状态由renderNotes方法处理
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const icon = document.getElementById('toastIcon');
        const messageEl = document.getElementById('toastMessage');

        // 设置图标和样式
        switch (type) {
            case 'success':
                icon.className = 'fas fa-check-circle text-green-500';
                break;
            case 'error':
                icon.className = 'fas fa-exclamation-circle text-red-500';
                break;
            case 'warning':
                icon.className = 'fas fa-exclamation-triangle text-yellow-500';
                break;
            default:
                icon.className = 'fas fa-info-circle text-blue-500';
        }

        messageEl.textContent = message;

        // 显示toast
        toast.classList.remove('translate-x-full');

        // 自动隐藏
        setTimeout(() => {
            toast.classList.add('translate-x-full');
        }, 3000);
    }

    // 刷新数据
    async refresh() {
        await this.loadCategories();
        await this.loadTags();
        await this.loadNotes();
    }
}

// 全局实例
let notesManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    notesManager = new NotesManager();
    window.notesManager = notesManager; // 供其他模块使用

    // 绑定toast关闭按钮
    document.getElementById('toastClose').addEventListener('click', () => {
        document.getElementById('toast').classList.add('translate-x-full');
    });
});
