<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易价格计算</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <!-- 添加Chart.js引用 - 注意加载顺序 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/luxon/2.5.2/luxon.min.js"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-adapter-luxon/1.3.1/chartjs-adapter-luxon.min.js"></script>
    <link rel="stylesheet" href="css/price.css">
    <script>
        // 确保适配器正确注册
        document.addEventListener('DOMContentLoaded', function () {
            if (!window.Chart || !window.luxon || !window._adapters) {
                console.warn('Chart.js或日期适配器加载不完整，将尝试使用替代方案');
            }
        });
    </script>
</head>

<body class="dark-mode">
    <div class="container animate__animated animate__fadeIn">
        <button class="back-button left clay-button" onclick="history.back()" title="返回">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="header-buttons">
            <a href="/analytics-enhanced.html" id="analyticsButton" class="clay-button" title="数据分析 (仅管理员)"
                onclick="return checkAnalyticsAccess()">
                <i class="fas fa-chart-bar"></i>
            </a>
            <button class="theme-toggle clay-button" id="themeToggle" title="切换主题">
                <i class="fas fa-sun"></i>
            </button>
        </div>

        <h1 class="animate__animated animate__fadeInDown">价格计算器</h1>

        <!-- 使用flex布局实现左右结构 -->
        <div class="main-content">
            <!-- 左侧输入区域 -->
            <div class="left-panel animate__animated animate__fadeInUp">
                <!-- 简化的输入区域 -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="price">价格</label>
                            <input type="number" id="price" placeholder="输入价格">
                            <div id="price-error" class="error-message"></div>
                        </div>
                        <div class="form-group">
                            <label for="operationType">类型</label>
                            <select id="operationType">
                                <option value="更换">更换</option>
                                <option value="加装">加装</option>
                                <option value="减配">减配</option>
                                <option value="平替">平替</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="config">描述</label>
                            <input type="text" id="config" placeholder="配置信息">
                            <div id="config-error" class="error-message"></div>
                        </div>
                    </div>
                    <button class="add-button" id="addButton">添加项目</button>
                </div>

                <!-- 常用配件快捷按钮 -->
                <div class="form-section">
                    <h3 style="margin-top:0;color:var(--clay-text);font-size:16px;margin-bottom:15px;">常用配件</h3>

                    <!-- 新增：包装盒运费模块 -->
                    <h4 style="margin-top:0;color:var(--clay-secondary-text);font-size:14px;margin-bottom:10px;">
                        <i class="fas fa-shipping-fast" style="margin-right:5px;"></i> 包装盒运费
                    </h4>
                    <div class="quick-items" style="margin-bottom:20px;">
                        <button class="quick-button price-20" onclick="quickAdd(20, '加装', '包装盒运费')">
                            <i class="fas fa-box"></i>  <span class="price-badge">20元</span>
                        </button>
                        <button class="quick-button price-30" onclick="quickAddDouyin(30, '加装', '包装盒运费')">
                            <i class="fas fa-box"></i> <10000 <span class="price-badge">30元</span>
                        </button>
                        <button class="quick-button price-50" onclick="quickAddDouyin(50, '加装', '包装盒运费')">
                            <i class="fas fa-box"></i> >10000 <span class="price-badge">50元</span>
                        </button>
                    </div>

                    <!-- 风扇部分 -->
                    <h4 style="margin-top:0;color:var(--clay-secondary-text);font-size:14px;margin-bottom:10px;">
                        <i class="fas fa-fan" style="margin-right:5px;"></i> 风扇系列
                    </h4>
                    <div class="quick-items" style="margin-bottom:20px;">
                        <button class="quick-button price-30" onclick="quickAdd(30, '加装', '风扇')">
                            <i class="fas fa-fan"></i> 风扇 <span class="price-badge">30元</span>
                        </button>
                        <button class="quick-button price-30" onclick="quickAddMultiple(30, '加装', '风扇')">
                            <i class="fas fa-fan"></i> 多把风扇 <span class="price-badge">30元</span>
                        </button>
                    </div>
                    <div class="quick-items" style="margin-bottom:20px;">
                        <button class="quick-button price-20" onclick="quickAdd(20, '加装', '风扇')">
                            <i class="fas fa-fan"></i> 风扇 <span class="price-badge">20元</span>
                        </button>
                        <button class="quick-button price-20" onclick="quickAddMultiple(20, '加装', '风扇')">
                            <i class="fas fa-fan"></i> 多把风扇 <span class="price-badge">20元</span>
                        </button>
                    </div>
                    <!-- 集线器部分 -->
                    <h4 style="margin-top:0;color:var(--clay-secondary-text);font-size:14px;margin-bottom:10px;">
                        <i class="fas fa-project-diagram" style="margin-right:5px;"></i> 集线器系列
                    </h4>
                    <div class="quick-items">
                        <button class="quick-button price-50" onclick="quickAdd(50, '加装', '集线器')">
                            <i class="fas fa-project-diagram"></i> 集线器 <span class="price-badge">50元</span>
                        </button>
                        <button class="quick-button price-40" onclick="quickAdd(40, '加装', '集线器')">
                            <i class="fas fa-project-diagram"></i> 集线器 <span class="price-badge">40元</span>
                        </button>
                    </div>

                    <!-- 特殊功能部分 -->
                    <h4 style="margin-top:20px;color:var(--clay-secondary-text);font-size:14px;margin-bottom:10px;">
                        <i class="fas fa-tools" style="margin-right:5px;"></i> 特殊功能
                    </h4>
                    <div class="quick-items" style="margin-bottom:10px;">
                        <button class="quick-button price-orange" onclick="openFanPositionModal()">
                            <i class="fas fa-fan"></i> 机箱风扇位置
                        </button>
                        <button class="quick-button price-special" onclick="openBuchaModal()">
                            <i class="fas fa-calculator"></i> 差价链接计算
                        </button>
                    </div>
                    <div class="quick-items quick-items-single-row" style="margin-bottom:20px;">
                        <button class="quick-button price-50" onclick="openInternalNotesModal()">
                            <i class="fas fa-sticky-note"></i> 内部备注
                        </button>
                    </div>

                    <!-- 数据管理部分 -->
                    <h4 style="margin-top:20px;color:var(--clay-secondary-text);font-size:14px;margin-bottom:10px;">
                        <i class="fas fa-chart-bar" style="margin-right:5px;"></i> 数据管理
                    </h4>
                    <div class="quick-items" style="margin-bottom:20px;">
                        <button class="quick-button price-30" onclick="openDataAnalysisModal()">
                            <i class="fas fa-chart-line"></i> 数据分析
                        </button>
                        <button class="quick-button price-40" onclick="openRawTableModal()">
                            <i class="fas fa-table"></i> 原始表格数据
                        </button>
                    </div>
                </div>

                <!-- 恢复转换器模块 -->
                <div class="form-section">
                    <div style="margin-bottom: 10px;">
                        <label for="rawInput"
                            style="display: block; margin-bottom: 8px; font-weight: 500; color: var(--clay-secondary-text); font-size: 14px;">表格数据转换</label>
                        <textarea id="rawInput" placeholder="粘贴表格数据（例如从Excel复制的数据）..."
                            style="width: 100%; height: 100px; border: none; border-radius: 15px; padding: 10px; font-size: 14px; background-color: var(--clay-container-bg); color: var(--clay-text); box-shadow: var(--clay-shadow-button-active);"></textarea>
                    </div>
                    <button class="add-button" onclick="processTableData()" style="width: 100%;">
                        <i class="fas fa-table"></i> 显示表格数据
                    </button>
                </div>
            </div>

            <!-- 右侧结果区域 -->
            <div class="right-panel animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <!-- 整合的输入和结果区域 -->
                <div class="form-section">
                    <textarea id="inputText" class="dynamic-input" placeholder="输入价格项目后将自动计算总额..."
                        oninput="autoCalculate()"></textarea>
                </div>

                <!-- 总价显示区域 -->
                <div class="result-container">
                    <div style="text-align: center;">
                        <div class="total-label">总金额</div>
                        <div class="total-amount" id="totalAmount">0</div>
                    </div>
                </div>

                <!-- 操作按钮区域 -->
                <div class="button-container" style="margin-top: 20px;">
                    <button class="button reset-button" onclick="resetForm()" style="margin-right: 10px;">
                        <i class="fas fa-trash"></i> 清空所有
                    </button>
                    <button class="button calculate-button" onclick="copyToClipboard(); animateCSS(this, 'pulse');">
                        <i class="fas fa-copy"></i> 复制结果
                    </button>
                </div>

                <!-- 邀请定制计算器模块 -->
                <div class="form-section" style="margin-top: 20px;">
                    <h3 style="margin-top:0;color:var(--clay-text);font-size:16px;margin-bottom:15px;">
                        <i class="fas fa-calculator" style="margin-right:5px;"></i> 邀请定制计算器
                    </h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="machinePrice">整机价格</label>
                            <input type="number" id="machinePrice" placeholder="输入整机价格" min="0"
                                oninput="calculateCustomTotal()">
                        </div>
                        <div class="form-group">
                            <label for="extraPrice">补差价格</label>
                            <input type="number" id="extraPrice" placeholder="输入补差价格" min="0"
                                oninput="calculateCustomTotal()">
                        </div>
                    </div>

                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                        <div style="font-size: 18px; color: var(--clay-primary);">
                            总价: <span id="customTotalPrice">0</span> 元
                        </div>
                        <div>
                            <!-- <button class="button calculate-button" onclick="calculateCustomTotal(true)" style="margin-right: 10px;">
                                <i class="fas fa-calculator"></i> 计算总价
                            </button> -->
                            <button class="button calculate-button" onclick="copyCustomTotal()">
                                <i class="fas fa-copy"></i> 复制总价
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 风扇和集线器成本计算器 -->
                <div class="form-section" style="margin-top: 20px;">
                    <h3 style="margin-top:0;color:var(--clay-text);font-size:16px;margin-bottom:15px;">
                        <i class="fas fa-fan" style="margin-right:5px;"></i> 风扇集线器利润计算器
                    </h3>

                    <!-- 风扇计算区域 -->
                    <div
                        style="background-color: var(--clay-container-bg); padding: 15px; border-radius: 15px; margin-bottom: 15px;">
                        <h4 style="margin-top:0;color:var(--clay-text);font-size:14px;margin-bottom:10px;">
                            <i class="fas fa-fan" style="margin-right:5px;"></i> 风扇利润计算
                        </h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="fanQuantity">风扇数量</label>
                                <input type="number" id="fanQuantity" placeholder="输入风扇数量" min="0"
                                    oninput="calculateFanProfit()">
                            </div>
                            <div class="form-group">
                                <label for="fanPrice">单价类型</label>
                                <select id="fanPrice" onchange="calculateFanProfit()">
                                    <option value="30">30元/把</option>
                                    <option value="20">20元/把</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>总售价</label>
                                <div
                                    style="padding: 12px; background-color: var(--clay-bg); border-radius: 10px; color: var(--clay-text); font-weight: bold;">
                                    <span id="fanTotalPrice">0</span> 元
                                </div>
                            </div>
                        </div>
                        <div
                            style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                            <div style="font-size: 16px; color: var(--clay-primary);">
                                成本: <span id="fanCost">0</span> 元 | 利润: <span id="fanProfit"
                                    style="font-weight: bold;">0</span> 元
                            </div>
                        </div>
                    </div>

                    <!-- 集线器计算区域 -->
                    <div
                        style="background-color: var(--clay-container-bg); padding: 15px; border-radius: 15px; margin-bottom: 15px;">
                        <h4 style="margin-top:0;color:var(--clay-text);font-size:14px;margin-bottom:10px;">
                            <i class="fas fa-project-diagram" style="margin-right:5px;"></i> 集线器利润计算
                        </h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="hubQuantity">集线器数量</label>
                                <input type="number" id="hubQuantity" placeholder="输入集线器数量" min="0"
                                    oninput="calculateHubProfit()">
                            </div>
                            <div class="form-group">
                                <label for="hubSellPrice">单价类型</label>
                                <select id="hubSellPrice" onchange="calculateHubProfit()">
                                    <option value="50">50元/个</option>
                                    <option value="40">40元/个</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>总售价</label>
                                <div
                                    style="padding: 12px; background-color: var(--clay-bg); border-radius: 10px; color: var(--clay-text); font-weight: bold;">
                                    <span id="hubTotalPrice">0</span> 元
                                </div>
                            </div>
                        </div>
                        <div
                            style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                            <div style="font-size: 16px; color: var(--clay-primary);">
                                成本: <span id="hubCost">0</span> 元 | 利润: <span id="hubProfit"
                                    style="font-weight: bold;">0</span> 元
                            </div>
                        </div>
                    </div>

                    <!-- 总计区域 -->
                    <div class="profit-total-section" style="padding: 15px; border-radius: 15px; text-align: center;">
                        <div style="font-size: 18px; font-weight: bold; margin-bottom: 10px;">
                            总利润: <span id="totalProfit">0</span> 元
                        </div>
                        <div style="display: flex; justify-content: center; gap: 15px;">
                            <button class="button reset-button" onclick="resetProfitCalculator()"
                                style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                <i class="fas fa-refresh"></i> 重置
                            </button>
                            <button class="button calculate-button" onclick="copyProfitResult()"
                                style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);">
                                <i class="fas fa-copy"></i> 复制结果
                            </button>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- 添加历史记录区域 - 保持全宽 -->
        <div class="form-section animate__animated animate__fadeInUp" style="margin-top: 20px; animation-delay: 0.4s;">
            <h3 style="margin-top:0;color:var(--clay-text);font-size:16px;margin-bottom:15px;">历史记录查询</h3>
            <div class="form-row">
                <div class="form-group" style="flex: 3;">
                    <input type="text" id="searchInput" placeholder="搜索关键词..." style="width: 100%;">
                </div>
                <div class="form-group" style="flex: 1;">
                    <select id="searchType">
                        <option value="all">全部</option>
                        <option value="description">按描述</option>
                        <option value="price">按金额</option>
                        <option value="date">按日期</option>
                    </select>
                </div>
                <div class="form-group" style="flex: 1;">
                    <button class="button calculate-button" onclick="searchRecords()" style="width: 100%; margin: 0;">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                <div class="form-group" style="flex: 1;">
                    <button class="button reset-button" onclick="resetHistorySearch()" style="width: 100%; margin: 0;">
                        <i class="fas fa-times"></i> 重置
                    </button>
                </div>
            </div>



            <!-- 历史记录列表 -->
            <div id="historyContainer" style="margin-top: 15px; max-height: 300px; overflow-y: auto;">
                <table style="width: 100%; border-collapse: collapse; color: var(--clay-text);">
                    <thead>
                        <tr style="background-color: transparent;">
                            <th style="padding: 10px; text-align: center;">ID</th>
                            <th style="padding: 10px; text-align: center;">日期</th>
                            <th style="padding: 10px; text-align: center;">配件数量</th>
                            <th style="padding: 10px; text-align: center;">配件明细</th>
                            <th style="padding: 10px; text-align: center;">总金额</th>
                            <th id="creatorHeader" style="padding: 10px; text-align: center; display: none;">创建者</th>
                            <th style="padding: 10px; text-align: center;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="historyList">
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 20px;"><i
                                    class="fas fa-spinner fa-spin"></i> 正在加载历史记录...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页控件 -->
            <div class="pagination-container">
                <span id="recordCount" class="pagination-info">总记录: 0</span>
                <div>
                    <button class="pagination-button" id="prevPage" onclick="changePage(-1)" disabled>上一页</button>
                    <span id="currentPage" style="margin: 0 10px; color: var(--clay-text);">第1页</span>
                    <button class="pagination-button" id="nextPage" onclick="changePage(1)" disabled>下一页</button>
                </div>
            </div>
        </div>

        <!-- 详情弹窗 -->
        <div id="detailModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="detailTitle" class="modal-title">详情</h3>
                    <button onclick="closeDetailModal()" class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="recordDetail" class="modal-body"></div>
                <div class="modal-footer">
                    <button class="button reset-button" onclick="closeDetailModal()"
                        style="min-width: 80px; flex: none;">关闭</button>
                    <button id="detailActionButton" class="button calculate-button" onclick="loadSelectedRecord()"
                        style="min-width: 120px; flex: none;">应用此记录</button>
                </div>
            </div>
        </div>

        <!-- 删除确认弹窗 -->
        <div id="deleteModal" class="modal">
            <div class="modal-content" style="max-width: 450px; text-align: center;">
                <div style="font-size: 60px; color: #E57373; margin-bottom: 20px;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 style="font-size: 24px; margin-bottom: 20px; color: var(--clay-text);">确认删除</h3>
                <p id="deleteConfirmText"
                    style="margin-bottom: 30px; color: var(--clay-secondary-text); font-size: 16px; line-height: 1.6;">
                    您确定要删除此记录吗？此操作不可撤销。</p>
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button class="button reset-button" onclick="closeDeleteModal()"
                        style="min-width: 120px;">取消</button>
                    <button id="confirmDeleteButton" class="button danger-button"
                        style="min-width: 120px;">确认删除</button>
                </div>
            </div>
        </div>

        <!-- 数量输入模态框 -->
        <div id="quantityModal" class="modal">
            <div class="modal-content" style="max-width: 450px; text-align: center;">
                <div style="font-size: 60px; color: var(--clay-primary); margin-bottom: 20px;">
                    <i class="fas fa-calculator"></i>
                </div>
                <h3 style="font-size: 24px; margin-bottom: 20px; color: var(--clay-text);">请输入数量</h3>
                <div style="margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                        <button onclick="decrementQuantity()" class="action-btn"
                            style="width: 48px; height: 48px; font-size: 24px; background-color: var(--clay-container-bg); color: var(--clay-text);">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" id="quantityInput" min="1" value="3"
                            style="width: 60%; padding: 15px; border-radius: 15px; border: none; background-color: var(--clay-container-bg); color: var(--clay-text); font-size: 24px; text-align: center; font-weight: bold; box-shadow: var(--clay-shadow-button-active);"
                            onkeypress="handleQuantityKeyPress(event)">
                        <button onclick="incrementQuantity()" class="action-btn"
                            style="width: 48px; height: 48px; font-size: 24px;">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div
                        style="margin-top: 20px; display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; max-width: 250px; margin-left: auto; margin-right: auto;">
                        <button onclick="setQuantity(1)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">1</button>
                        <button onclick="setQuantity(2)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">2</button>
                        <button onclick="setQuantity(3)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">3</button>
                        <button onclick="setQuantity(4)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">4</button>
                        <button onclick="setQuantity(5)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">5</button>
                        <button onclick="setQuantity(6)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">6</button>
                        <button onclick="setQuantity(7)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">7</button>
                        <button onclick="setQuantity(8)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">8</button>
                        <button onclick="setQuantity(9)" class="pagination-button"
                            style="height: 60px; font-size: 22px; font-weight: bold;">9</button>
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <button onclick="setQuantity(10)" class="pagination-button"
                            style="width: 80px; height: 60px; font-size: 22px; font-weight: bold;">10</button>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 20px; display: flex; justify-content: center; gap: 15px;">
                    <button class="button reset-button" onclick="closeQuantityModal()"
                        style="min-width: 120px;">取消</button>
                    <button class="button calculate-button" onclick="confirmQuantity()"
                        style="min-width: 120px;">
                        <i class="fas fa-check"></i> 确定
                    </button>
                </div>
            </div>
        </div>

        <!-- 原始表格数据模态框 -->
        <div id="rawTableModal" class="modal">
            <div class="modal-content" style="max-width: 1000px; width: 90%;">
                <div class="modal-header">
                    <h3 class="modal-title">原始表格数据库</h3>
                    <button onclick="closeRawTableModal()" class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <h3
                        style="margin-top:0;color:var(--clay-text);font-size:18px;margin-bottom:15px;text-align:center;">
                        <i class="fas fa-table" style="margin-right: 10px;"></i> 这里保存的是转换前的原始表格数据
                    </h3>

                    <div style="margin-bottom: 20px; border-radius: 8px; padding: 15px;">
                        <div class="form-row">
                            <div class="form-group" style="flex: 3;">
                                <input type="text" id="rawTableSearchInput" placeholder="搜索关键词..." style="width: 100%;">
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <select id="rawTableSearchType">
                                    <option value="all">全部</option>
                                    <option value="raw_data">按内容</option>
                                    <option value="date">按日期</option>
                                </select>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <button class="button calculate-button" onclick="searchRawTables()"
                                    style="width: 100%; margin: 0;">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="form-group" style="flex: 1;">
                                <button class="button reset-button" onclick="resetRawTableSearch()"
                                    style="width: 100%; margin: 0;">
                                    <i class="fas fa-times"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 原始表格数据列表 -->
                    <div class="table-container">
                        <table class="raw-data-table">
                            <thead>
                                <tr>
                                    <th style="width: 60px;">ID</th>
                                    <th style="width: 180px;">日期</th>
                                    <th style="text-align: right; width: 100px;">总金额</th>
                                    <th>数据预览</th>
                                    <th id="rawTableCreatorHeader"
                                        style="width: 100px; text-align: center; display: none;">创建者</th>
                                    <th style="width: 120px; text-align: center;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="rawTableList">
                                <tr>
                                    <td colspan="6" style="text-align: center; padding: 20px;">正在加载数据...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="pagination-container">
                        <span id="rawTableRecordCount" class="pagination-info">总记录: 0</span>
                        <div>
                            <button class="pagination-button" id="rawTablePrevPage" onclick="changeRawTablePage(-1)"
                                disabled>上一页</button>
                            <span id="rawTableCurrentPage" style="margin: 0 10px; color: var(--clay-text);">第1页</span>
                            <button class="pagination-button" id="rawTableNextPage" onclick="changeRawTablePage(1)"
                                disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 原始表格详情模态框 -->
        <div id="rawTableDetailModal" class="modal">
            <div class="modal-content" style="max-width: 900px; width: 90%;">
                <div class="modal-header">
                    <h3 id="rawTableDetailTitle" class="modal-title">表格数据详情</h3>
                    <button onclick="closeRawTableDetailModal()" class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="rawTableDetail" class="modal-body"></div>
                <div class="modal-footer">
                    <button class="button reset-button" onclick="closeRawTableDetailModal()"
                        style="min-width: 80px;">关闭</button>
                    <button class="button calculate-button" onclick="applyRawTableData()" style="min-width: 120px;">
                        应用此数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据分析模态框 -->
    <div id="dataAnalysisModal" class="modal">
        <div class="modal-content" style="max-width: 1000px; width: 90%;">
            <div class="modal-header">
                <h3 class="modal-title">数据分析</h3>
                <button onclick="closeDataAnalysisModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <!-- 时间范围过滤器 -->
                <div class="data-filter">
                    <label for="timeRange">时间范围：</label>
                    <select id="timeRange" onchange="updateAnalysisCharts()">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="180">最近180天</option>
                        <option value="365">最近一年</option>
                        <option value="0">全部时间</option>
                    </select>
                </div>

                <!-- 每日金额曲线图 -->
                <div class="form-section">
                    <div class="chart-title">每日总金额趋势</div>
                    <div class="chart-description">展示每天的订单总金额变化趋势，可以看出业务收入波动。</div>
                    <div class="chart-container" id="dailyAmountChartContainer">
                        <!-- canvas将在JS中动态创建 -->
                    </div>
                </div>

                <!-- 时段分布图 -->
                <div class="form-section">
                    <div class="chart-title">使用时间段分布</div>
                    <div class="chart-description">展示不同时间段的使用频率分布，帮助了解客户活跃时间。</div>
                    <div class="chart-container" id="timeDistributionChartContainer">
                        <!-- canvas将在JS中动态创建 -->
                    </div>
                </div>

                <!-- 配件类型统计图 -->
                <div class="form-section">
                    <div class="chart-title">配件类型占比</div>
                    <div class="chart-description">展示不同配件类型的数量占比，了解热门配件。</div>
                    <div class="chart-container" id="partTypeChartContainer">
                        <!-- canvas将在JS中动态创建 -->
                    </div>
                </div>

                <!-- 新增：操作类型分布图 -->
                <div class="form-section">
                    <div class="chart-title">操作类型分布</div>
                    <div class="chart-description">分析"加装"与"更换"等操作类型的比例，了解业务构成。</div>
                    <div class="chart-container" id="operationTypeChartContainer">
                        <!-- canvas将在JS中动态创建 -->
                    </div>
                </div>

                <!-- 新增：每日平均订单金额 -->
                <div class="form-section">
                    <div class="chart-title">每日平均订单金额 (AOV)</div>
                    <div class="chart-description">追踪每日平均订单价值，反映客户消费能力的变化。</div>
                    <div class="chart-container" id="dailyAOVChartContainer">
                        <!-- canvas将在JS中动态创建 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="button reset-button" onclick="closeDataAnalysisModal()"
                    style="min-width: 120px;">关闭</button>
                <button class="button calculate-button" onclick="refreshAnalysisData()" style="min-width: 120px;">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
            </div>
        </div>
    </div>

    <!-- 新增：差价计算模态框 -->
    <div id="buchaModal" class="modal">
        <div class="modal-content" style="max-width: 650px;">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-calculator" style="margin-right: 10px;"></i>差价计算工具</h3>
                <button onclick="closeBuchaModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p
                    style="text-align: center; color: var(--clay-secondary-text); margin-top: -10px; margin-bottom: 25px;">
                    输入需要补差的金额，系统自动计算需要拍下的差价链接数量。
                </p>

                <div class="form-section" style="padding: 25px;">
                    <div class="form-group">
                        <label for="buchaAmount">
                            <i class="fas fa-money-bill-wave"></i> 需要补差的金额 (元)
                        </label>
                        <input type="number" id="buchaAmount" placeholder="请输入金额，例如：560" min="1">
                    </div>
                </div>

                <div style="text-align: center; margin-bottom: 15px;">
                    <p style="font-size: 16px; margin-bottom: 10px; color: var(--clay-secondary-text);">选择补差方式:</p>
                    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                        <button id="normalBuchaBtn" class="button calculate-button"
                            style="flex: 1; background-color: var(--clay-primary);">
                            <i class="fas fa-coins"></i> 50/100元链接
                        </button>
                        <button id="bucha200Btn" class="button calculate-button" style="flex: 1;">
                            <i class="fas fa-money-bill-wave"></i> 200元链接
                        </button>
                        <button id="largeBuchaBtn" class="button calculate-button" style="flex: 1;">
                            <i class="fas fa-money-bill-wave"></i> 10000元链接
                        </button>
                    </div>
                    <div class="button-container" style="justify-content: center;">
                        <button id="buchaResetBtn" class="button reset-button" style="flex: 1;">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                    </div>
                    <!-- 隐藏计算按钮，改为直接在选择方式时计算 -->
                    <button id="buchaCalculateBtn" class="button calculate-button" style="display: none;">
                        <i class="fas fa-calculator"></i> 计算补差方案
                    </button>
                </div>

                <div id="buchaResult" class="form-section" style="margin-top: 20px; display: none; position: relative;">
                    <h3
                        style="margin-top:0; color:var(--clay-text); font-size:18px; margin-bottom:15px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-clipboard-list"></i> 补差方案
                    </h3>
                    <div id="buchaResultContent" style="line-height: 1.7; color: var(--clay-text);"></div>
                    <button class="button calculate-button" id="buchaCopyBtn"
                        style="position: absolute; top: 20px; right: 20px; padding: 10px 15px; font-size: 14px;">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>

                <div class="form-section" style="margin-top: 20px;">
                    <h3
                        style="margin-top:0; color:var(--clay-text); font-size:18px; margin-bottom:15px; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-info-circle"></i> 补差操作说明
                    </h3>
                    <ul
                        style="padding-left: 20px; color: var(--clay-secondary-text); font-size: 14px; line-height: 1.8;">
                        <li>请按计算结果拍下相应数量的差价链接。</li>
                        <li>拍下后<span
                                style="color: var(--color-error); background: transparent; font-weight: bold;">不要付款</span>，请联系客服改价。
                        </li>
                        <li>进入支付页面后<span
                                style="color: var(--color-error); background: transparent; font-weight: bold;">不要刷脸、不要输入支付密码</span>。
                        </li>
                        <li>差价链接<span
                                style="color: var(--color-error); background: transparent; font-weight: bold;">不可使用优惠券</span>，否则无法改价。
                        </li>
                        <li>等待客服修改价格后再完成支付。</li>
                        <li><span
                                style="color: var(--clay-primary); background: transparent; font-weight: bold;">50/100元链接</span>：金额≤50元时使用50元链接，金额＞50元时使用100元链接。
                        </li>
                        <li><span
                                style="color: var(--clay-primary); background: transparent; font-weight: bold;">10000元链接</span>：适用于大额补差，拍下后联系客服改为实际需要的金额。
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加表格编辑模态框 -->
    <div id="tableEditModal" class="modal">
        <div class="modal-content" style="max-width: 90%; width: 1100px;">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-table" style="margin-right: 10px;"></i>表格数据编辑</h3>
                <button onclick="hideTableEditor()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div
                    style="margin-bottom: 15px; display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center;">
                    <span style="margin-bottom: 5px;">您可以直接编辑表格数据，然后点击转换按钮将数据转换为价格条目。</span>
                </div>
                <div
                    style="background-color: var(--input-bg-color); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <div
                        style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="margin: 0;">表格数据</h4>
                        <div style="display: flex; align-items: center;">
                            <div class="search-container" style="position: relative; flex-grow: 1; max-width: 300px;">
                                <input type="text" id="tableSearchInput" placeholder="搜索表格..." />
                                <button onclick="searchTable()" class="search-button"
                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">
                                    <i class="fas fa-search" style="color: var(--clay-secondary-text);"></i>
                                </button>
                            </div>
                            <button onclick="searchTable()" class="action-btn"
                                style="margin-left: 8px; height: 36px; width: 36px;">
                                <i class="fas fa-search"></i>
                            </button>
                            <button onclick="clearTableSearch()" class="reset-button"
                                style="margin-left: 8px; height: 36px; padding: 0 15px; border-radius: 18px; font-size: 14px;">
                                <i class="fas fa-times"></i> 重置
                            </button>
                        </div>
                    </div>
                    <div id="editableTableContainer" class="table-container"
                        style="padding-left: 30px; position: relative; overflow-x: auto;">
                        <!-- 表格将在JS中动态创建 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="button calculate-button" onclick="copyTableData()" style="min-width: 120px;">
                    <i class="fas fa-copy"></i> 复制
                </button>
                <button class="button reset-button" onclick="hideTableEditor()"
                    style="min-width: 120px; margin-left: 10px;">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button class="button calculate-button" onclick="saveTableAndConvert()"
                    style="min-width: 120px; margin-left: 10px;">
                    <i class="fas fa-exchange-alt"></i> 转换数据
                </button>
            </div>
        </div>
    </div>

    <!-- 新增：风扇位置选择模态框 -->
    <div id="fanPositionModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-fan" style="margin-right: 10px;"></i>机箱风扇位置</h3>
                <button onclick="closeFanPositionModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p style="text-align: center; color: var(--clay-secondary-text); margin-bottom: 20px;">
                    请输入各个位置的风扇数量（尾部/上/前为正装，侧面/下为反装）
                </p>

                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 20px;">
                    <div
                        style="text-align: center; padding: 10px; background-color: rgba(255, 82, 82, 0.1); border-radius: 10px;">
                        <label for="tailFans"
                            style="display: block; font-weight: bold; margin-bottom: 5px; color: #ff5252;">尾部
                            (正)</label>
                        <input type="number" id="tailFans" min="0" max="9" placeholder="数量"
                            style="width: 80%; text-align: center; font-size: 18px; background-color: rgba(0, 0, 0, 0.2); border: none; color: white;">
                    </div>

                    <div
                        style="text-align: center; padding: 10px; background-color: rgba(255, 82, 82, 0.1); border-radius: 10px;">
                        <label for="topFans"
                            style="display: block; font-weight: bold; margin-bottom: 5px; color: #ff5252;">上部
                            (正)</label>
                        <input type="number" id="topFans" min="0" max="9" placeholder="数量"
                            style="width: 80%; text-align: center; font-size: 18px; background-color: rgba(0, 0, 0, 0.2); border: none; color: white;">
                    </div>

                    <div
                        style="text-align: center; padding: 10px; background-color: rgba(255, 82, 82, 0.1); border-radius: 10px;">
                        <label for="frontFans"
                            style="display: block; font-weight: bold; margin-bottom: 5px; color: #ff5252;">前部
                            (正)</label>
                        <input type="number" id="frontFans" min="0" max="9" placeholder="数量"
                            style="width: 80%; text-align: center; font-size: 18px; background-color: rgba(0, 0, 0, 0.2); border: none; color: white;">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-bottom: 20px;">
                    <div
                        style="text-align: center; padding: 10px; background-color: rgba(66, 133, 244, 0.1); border-radius: 10px;">
                        <label for="sideFans"
                            style="display: block; font-weight: bold; margin-bottom: 5px; color: #4285f4;">侧面
                            (反)</label>
                        <input type="number" id="sideFans" min="0" max="9" placeholder="数量"
                            style="width: 80%; text-align: center; font-size: 18px; background-color: rgba(0, 0, 0, 0.2); border: none; color: white;">
                    </div>

                    <div
                        style="text-align: center; padding: 10px; background-color: rgba(66, 133, 244, 0.1); border-radius: 10px;">
                        <label for="bottomFans"
                            style="display: block; font-weight: bold; margin-bottom: 5px; color: #4285f4;">底部
                            (反)</label>
                        <input type="number" id="bottomFans" min="0" max="9" placeholder="数量"
                            style="width: 80%; text-align: center; font-size: 18px; background-color: rgba(0, 0, 0, 0.2); border: none; color: white;">
                    </div>
                </div>

                <!-- 实时反馈区域 -->
                <div style="margin-top: 20px; text-align: center;">
                    <div id="fanPositionResult"
                        style="display: none; padding: 15px; background-color: var(--clay-container-bg); border-radius: 10px; font-size: 20px; font-weight: bold; color: var(--clay-primary); margin-bottom: 15px;">
                    </div>
                    <p style="font-size: 12px; color: var(--clay-secondary-text); margin-bottom: 10px;">
                        示例：尾部1个，侧面2个 = "尾1正 侧2反<br>1正2反"
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="button reset-button" onclick="closeFanPositionModal()" style="min-width: 120px;">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button class="button calculate-button" onclick="processFanPosition()" style="min-width: 120px;">
                    <i class="fas fa-copy"></i> 复制结果
                </button>
            </div>
        </div>
    </div>

    <!-- 内部备注模态框 -->
    <div id="internalNotesModal" class="modal">
        <div class="modal-content" style="max-width: 700px;">
            <div class="modal-header">
                <h3 class="modal-title"><i class="fas fa-sticky-note" style="margin-right: 10px;"></i>内部备注</h3>
                <button onclick="closeInternalNotesModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p
                    style="text-align: center; color: var(--clay-secondary-text); margin-top: -10px; margin-bottom: 25px;">
                    编辑内部备注信息，点击复制按钮将内容复制到剪贴板
                </p>

                <div class="form-section" style="padding: 20px;">
                    <table id="internalNotesTable"
                        style="width: 100%; border-collapse: collapse; margin-bottom: 20px; border: 2px solid var(--clay-border);">
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; width: 120px; text-align: center;">
                                打包发货：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <input type="text" value="所有配件包装都要发"
                                    style="width: 100%; border: none; background: transparent; color: var(--clay-text); text-align: center; font-size: 14px;">
                            </td>
                        </tr>
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; text-align: center;">
                                显卡发货：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <input type="text" value="【显卡不拆封】"
                                    style="width: 100%; border: none; background: transparent; color: var(--clay-text); text-align: center; font-size: 14px;">
                            </td>
                        </tr>
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; text-align: center;">
                                主板调试：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <input type="text" value="刷最新BIOS,开XMP,开EXPO,双烤测试"
                                    style="width: 100%; border: none; background: transparent; color: var(--clay-text); text-align: center; font-size: 14px;">
                            </td>
                        </tr>
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; text-align: center;">
                                系统：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <input type="text" value="Windows11"
                                    style="width: 100%; border: none; background: transparent; color: var(--clay-text); text-align: center; font-size: 14px;">
                            </td>
                        </tr>
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; text-align: center;">
                                硬盘分区：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <div style="display: flex; gap: 5px; align-items: center;">
                                    <select id="diskPartitionSelect"
                                        style="flex: 1; padding: 4px; border: 1px solid var(--clay-border); border-radius: 4px; background: var(--clay-bg); color: var(--clay-text); font-size: 14px;"
                                        onchange="updateDiskPartition()">
                                        <option value="">选择预设方案</option>
                                        <option value="256G C盘120G, 其余D盘">256G C盘120G, 其余D盘</option>
                                        <option value="500G C盘200G ，其余D盘">500G C盘200G ，其余D盘</option>
                                        <option value="1T C盘300G，其余DE均分">1T C盘300G，其余DE均分</option>
                                        <option value="2T C盘300G，D 500G，剩余E">2T C盘300G，D 500G，剩余E</option>
                                    </select>
                                    <input type="text" id="diskPartitionInput" value="C盘300G，其余DE均分"
                                        style="flex: 2; border: 1px solid var(--clay-border); border-radius: 4px; padding: 4px; background: var(--clay-bg); color: var(--clay-text); text-align: center; font-size: 14px;"
                                        placeholder="或手动输入">
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td
                                style="border: 1px solid var(--clay-border); padding: 12px; background-color: var(--clay-container-bg); font-weight: bold; text-align: center;">
                                风扇安装：</td>
                            <td style="border: 1px solid var(--clay-border); padding: 8px; text-align: center;">
                                <input type="text" value="1正 尾1/6反 侧3底3"
                                    style="width: 100%; border: none; background: transparent; color: var(--clay-text); text-align: center; font-size: 14px;">
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button class="button reset-button" onclick="closeInternalNotesModal()" style="min-width: 120px;">
                    <i class="fas fa-times"></i> 关闭
                </button>
                <button class="button calculate-button" onclick="copyInternalNotes()" style="min-width: 120px;">
                    <i class="fas fa-copy"></i> 复制内容
                </button>
            </div>
        </div>
    </div>


</body>

<script src="js/price.js"></script>
<script src="js/quick-add-functions.js"></script>


</html>